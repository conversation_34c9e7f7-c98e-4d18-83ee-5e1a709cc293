'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw, Package, CheckCircle, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/goods-receipts/data-table';
import { createGoodsReceiptColumns } from '@/components/goods-receipts/columns';
import { GoodsReceiptQuickView } from '@/components/goods-receipts/goods-receipt-quick-view';

import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { GoodsReceipt, GoodsReceiptQueryParams, GoodsReceiptStatus, GoodsReceiptWithRelations } from '@/types/goods-receipt';
import {
  useGoodsReceipts,
  useGoodsReceipt,
  useGoodsReceiptsInvalidate,
  useRejectGoodsReceipt,
  useCompleteGoodsReceipt,
  useDeleteGoodsReceipt,
  useExportGoodsReceipts,
  useBulkUpdateGoodsReceiptStatus,
  useBulkDeleteGoodsReceipts,
} from '@/hooks/useGoodsReceipts';
import { toast } from 'sonner';
import { DEFAULT_GOODS_RECEIPT_PAGINATION } from '@/lib/constants/goods-receipt';

interface GoodsReceiptsPageClientProps {
  initialQuery: GoodsReceiptQueryParams;
}

export function GoodsReceiptsPageClient({ initialQuery }: GoodsReceiptsPageClientProps) {
  const router = useRouter();
  const [query, setQuery] = useState<GoodsReceiptQueryParams>(initialQuery);
  const [selectedGoodsReceiptId, setSelectedGoodsReceiptId] = useState<string | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);

  const [deleteGoodsReceipt, setDeleteGoodsReceipt] = useState<GoodsReceipt | null>(null);
  const [rejectGoodsReceipt, setRejectGoodsReceipt] = useState<GoodsReceipt | null>(null);

  // Queries and mutations
  const { data: goodsReceiptsResponse, isLoading, error } = useGoodsReceipts(query);
  const { data: selectedGoodsReceipt } = useGoodsReceipt(selectedGoodsReceiptId!);
  const invalidateGoodsReceipts = useGoodsReceiptsInvalidate();
  const rejectGoodsReceiptMutation = useRejectGoodsReceipt();
  const completeGoodsReceiptMutation = useCompleteGoodsReceipt();
  const deleteGoodsReceiptMutation = useDeleteGoodsReceipt();
  const exportGoodsReceiptsMutation = useExportGoodsReceipts();
  const bulkUpdateStatusMutation = useBulkUpdateGoodsReceiptStatus();
  const bulkDeleteMutation = useBulkDeleteGoodsReceipts();

  const goodsReceipts = goodsReceiptsResponse?.data || [];
  const meta = goodsReceiptsResponse?.meta;

  // Update query parameters
  const updateQuery = useCallback((updates: Partial<GoodsReceiptQueryParams>) => {
    const newQuery = { ...query, ...updates, page: 1 }; // Reset to first page when filtering
    setQuery(newQuery);

    // Update URL
    const searchParams = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.set(key, String(value));
      }
    });

    const newUrl = `/dashboard/goods-receipts?${searchParams.toString()}`;
    router.replace(newUrl, { scroll: false });
  }, [query, router]);

  // Handlers
  const handleView = useCallback((goodsReceipt: GoodsReceipt) => {
    setSelectedGoodsReceiptId(goodsReceipt.id);
    setShowQuickView(true);
  }, []);

  const handleViewDetails = useCallback((goodsReceipt: GoodsReceipt) => {
    router.push(`/dashboard/goods-receipts/${goodsReceipt.id}`);
  }, [router]);

  const handleEdit = useCallback((goodsReceipt: GoodsReceipt) => {
    router.push(`/dashboard/goods-receipts/${goodsReceipt.id}/edit`);
  }, [router]);

  const handleDelete = useCallback((goodsReceipt: GoodsReceipt) => {
    setDeleteGoodsReceipt(goodsReceipt);
  }, []);



  const handleReject = useCallback((goodsReceipt: GoodsReceipt) => {
    setRejectGoodsReceipt(goodsReceipt);
  }, []);

  const handleComplete = useCallback(async (goodsReceipt: GoodsReceipt) => {
    try {
      await completeGoodsReceiptMutation.mutateAsync(goodsReceipt.id);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [completeGoodsReceiptMutation]);



  const handleRefresh = useCallback(() => {
    invalidateGoodsReceipts();
  }, [invalidateGoodsReceipts]);

  const handleExport = useCallback(async () => {
    try {
      await exportGoodsReceiptsMutation.mutateAsync(query);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [exportGoodsReceiptsMutation, query]);

  const handleNewGoodsReceipt = useCallback(() => {
    router.push('/dashboard/goods-receipts/new');
  }, [router]);

  const handleGoToPage = useCallback(async (routerPath: string) => {
    router.push(routerPath);
  }, [])

  const confirmDelete = useCallback(async () => {
    if (!deleteGoodsReceipt) return;

    try {
      await deleteGoodsReceiptMutation.mutateAsync(deleteGoodsReceipt.id);
      setDeleteGoodsReceipt(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [deleteGoodsReceipt, deleteGoodsReceiptMutation]);

  const confirmReject = useCallback(async () => {
    if (!rejectGoodsReceipt) return;

    try {
      await rejectGoodsReceiptMutation.mutateAsync(rejectGoodsReceipt.id);
      setRejectGoodsReceipt(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [rejectGoodsReceipt, rejectGoodsReceiptMutation]);

  // Create columns with handlers
  const columns = createGoodsReceiptColumns({
    onView: handleView,
    onEdit: handleEdit,
    onDelete: handleDelete,
    onReject: handleReject,
    onComplete: handleComplete,
    goToPage: handleGoToPage,
  });

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat data penerimaan barang</p>
            <Button onClick={handleRefresh} variant="outline" className="mt-2">
              <RefreshCw className="mr-2 h-4 w-4" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {/* Header Actions */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Penerimaan Barang</h2>
            <p className="text-muted-foreground">
              Kelola penerimaan barang dan kontrol kualitas
            </p>
          </div>
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button onClick={handleNewGoodsReceipt} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Penerimaan Baru
            </Button>
          </div>
        </div>

        {/* Data Table */}
        <Card>
          <CardContent>
            <DataTable
              columns={columns}
              data={goodsReceipts}
              searchValue={query.search || ''}
              onSearchChange={(search) => updateQuery({ search })}
              statusFilter={query.status || ''}
              onStatusFilterChange={(status) => updateQuery({ status: status || undefined })}

              supplierFilter={query.supplierId || ''}
              onSupplierFilterChange={(supplierId) => updateQuery({ supplierId: supplierId || undefined })}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      </div>

      {/* Quick View Modal */}
      <GoodsReceiptQuickView
        goodsReceipt={selectedGoodsReceipt || null}
        open={showQuickView}
        onOpenChange={(open) => {
          setShowQuickView(open);
          if (!open) {
            setSelectedGoodsReceiptId(null);
          }
        }}
        onEdit={handleEdit}
        onReject={handleReject}
        onComplete={handleComplete}
        onDelete={handleDelete}
        onViewDetails={handleViewDetails}
      />



      {/* Delete Confirmation */}
      <AlertDialogWrapper
        open={!!deleteGoodsReceipt}
        onOpenChange={(open) => !open && setDeleteGoodsReceipt(null)}
        title="Hapus Penerimaan Barang"
        description={`Apakah Anda yakin ingin menghapus penerimaan barang ${deleteGoodsReceipt?.receiptNumber}? Tindakan ini tidak dapat dibatalkan.`}
        confirmText="Hapus"
        cancelText="Batal"
        onConfirm={confirmDelete}
        variant="destructive"
      />

      {/* Reject Confirmation */}
      <AlertDialogWrapper
        open={!!rejectGoodsReceipt}
        onOpenChange={(open) => !open && setRejectGoodsReceipt(null)}
        title="Tolak Penerimaan Barang"
        description={`Apakah Anda yakin ingin menolak penerimaan barang ${rejectGoodsReceipt?.receiptNumber}? Tindakan ini tidak dapat dibatalkan.`}
        confirmText="Tolak"
        cancelText="Batal"
        onConfirm={confirmReject}
        variant="destructive"
      />
    </>
  );
}
